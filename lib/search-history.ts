import { supabase } from './supabase';

export interface SearchHistoryEntry {
  id: string;
  query: string;
  timestamp: string;
  user_id?: string;
  session_id?: string;
}

// Generate a session ID for anonymous users
function getSessionId(): string {
  let sessionId = localStorage.getItem('search_session_id');
  if (!sessionId) {
    sessionId = `anon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem('search_session_id', sessionId);
  }
  return sessionId;
}

// Save a search query to the database
export async function saveSearchQuery(query: string, userId?: string): Promise<void> {
  try {
    // Don't save empty queries or system queries
    if (!query || query.trim().length === 0 || query.startsWith('__')) {
      return;
    }

    const trimmedQuery = query.trim().toLowerCase();
    
    // Don't save very short queries (less than 2 characters)
    if (trimmedQuery.length < 2) {
      return;
    }

    const sessionId = userId ? null : getSessionId();

    // Insert the search query into chat_messages table
    // We'll use this table to store search history alongside chat messages
    const { error } = await supabase
      .from('chat_messages')
      .insert({
        message: trimmedQuery,
        sender: 'user',
        user_id: userId || null,
        session_id: sessionId,
        created_at: new Date().toISOString(),
        // Mark this as a search query for easy filtering
        metadata: { type: 'search_query' }
      });

    if (error) {
      console.error('Error saving search query:', error);
    }
  } catch (error) {
    console.error('Error saving search query:', error);
  }
}

// Get recent search queries for suggestions
export async function getRecentSearchQueries(limit: number = 10): Promise<string[]> {
  try {
    const sessionId = getSessionId();

    // Get recent search queries from the current session or user
    const { data, error } = await supabase
      .from('chat_messages')
      .select('message')
      .or(`session_id.eq.${sessionId},user_id.is.null`)
      .eq('sender', 'user')
      .not('metadata->type', 'is', null) // Has metadata
      .order('created_at', { ascending: false })
      .limit(limit * 2); // Get more to filter duplicates

    if (error) {
      console.error('Error fetching recent searches:', error);
      return [];
    }

    if (!data || data.length === 0) {
      return [];
    }

    // Remove duplicates and filter out very short queries
    const uniqueQueries = Array.from(new Set(
      data
        .map(item => item.message)
        .filter(query => query && query.length >= 2)
    )).slice(0, limit);

    return uniqueQueries;
  } catch (error) {
    console.error('Error fetching recent searches:', error);
    return [];
  }
}

// Get popular search queries across all users
export async function getPopularSearchQueries(limit: number = 8): Promise<string[]> {
  try {
    // Get popular search queries from all users (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { data, error } = await supabase
      .from('chat_messages')
      .select('message')
      .eq('sender', 'user')
      .not('metadata->type', 'is', null) // Has metadata indicating it's a search query
      .gte('created_at', thirtyDaysAgo.toISOString())
      .order('created_at', { ascending: false })
      .limit(200); // Get a larger sample to analyze

    if (error) {
      console.error('Error fetching popular searches:', error);
      return getDefaultSuggestions();
    }

    if (!data || data.length === 0) {
      return getDefaultSuggestions();
    }

    // Count frequency of each query
    const queryCount: { [key: string]: number } = {};
    data.forEach(item => {
      const query = item.message?.toLowerCase().trim();
      if (query && query.length >= 2) {
        queryCount[query] = (queryCount[query] || 0) + 1;
      }
    });

    // Sort by frequency and return top queries
    const popularQueries = Object.entries(queryCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([query]) => query);

    // If we don't have enough popular queries, supplement with defaults
    if (popularQueries.length < limit) {
      const defaults = getDefaultSuggestions();
      const combined = [...popularQueries];
      
      for (const defaultQuery of defaults) {
        if (!combined.includes(defaultQuery.toLowerCase()) && combined.length < limit) {
          combined.push(defaultQuery.toLowerCase());
        }
      }
      
      return combined.slice(0, limit);
    }

    return popularQueries;
  } catch (error) {
    console.error('Error fetching popular searches:', error);
    return getDefaultSuggestions();
  }
}

// Default search suggestions when no history is available
function getDefaultSuggestions(): string[] {
  return [
    'cara daftar LPDP',
    'syarat beasiswa LPDP',
    'jadwal pendaftaran',
    'dokumen yang diperlukan',
    'universitas tujuan',
    'mata garuda',
    'beasiswa reguler',
    'beasiswa afirmasi'
  ];
}

// Clean up old search history (optional maintenance function)
export async function cleanupOldSearchHistory(daysToKeep: number = 90): Promise<void> {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const { error } = await supabase
      .from('chat_messages')
      .delete()
      .not('metadata->type', 'is', null) // Only delete search queries
      .lt('created_at', cutoffDate.toISOString());

    if (error) {
      console.error('Error cleaning up old search history:', error);
    }
  } catch (error) {
    console.error('Error cleaning up old search history:', error);
  }
}
