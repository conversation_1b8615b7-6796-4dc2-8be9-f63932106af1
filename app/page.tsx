'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Bot,
  Brain,
  Settings,
  Shield,
  LogOut,
  Sparkles,
  BookOpen,
  ArrowRight
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { AuthService } from '@/lib/auth';
import SearchSuggestions from '@/components/search/SearchSuggestions';
import SearchBox from '@/components/search/SearchBox';

interface KnowledgeStats {
  totalDocuments: number;
  topics: string[];
  sources: string[];
}

export default function HomePage() {
  const [user, setUser] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [knowledgeStats, setKnowledgeStats] = useState<KnowledgeStats | null>(null);
  const router = useRouter();

  useEffect(() => {
    // Load knowledge stats
    loadKnowledgeStats();

    // Check authentication status
    const checkAuth = async () => {
      const currentUser = await AuthService.getCurrentUser();
      setUser(currentUser);
    };
    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = AuthService.onAuthStateChange((user) => {
      setUser(user);
    });

    return () => subscription.unsubscribe();
  }, []);


  const loadKnowledgeStats = async () => {
    try {
      // We'll make a simple request to get stats without authentication
      const response = await fetch('/api/chat-public', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: '__GET_STATS__' }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.availableTopics || data.availableSources) {
          setKnowledgeStats({
            totalDocuments: data.availableSources?.length || 0,
            topics: data.availableTopics || [],
            sources: data.availableSources || []
          });
        }
      }
    } catch (error) {
      console.error('Error loading knowledge stats:', error);
    }
  };


  const handleSearch = (query: string) => {
    setIsLoading(true);
    try {
      // Navigate to search results page with query parameter
      router.push(`/search?q=${encodeURIComponent(query)}`);
    } catch (error) {
      console.error('Error navigating to search:', error);
      toast.error('Failed to perform search. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTopicClick = (topic: string) => {
    handleSearch(topic);
  };

  const handleSignOut = async () => {
    try {
      await AuthService.signOut();
      setUser(null);
      toast.success('Successfully signed out!');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  };




  return (
    <div className="min-h-screen bg-[#fffefa] flex flex-col">
      {/* Header */}
      <div className="border-b border-slate-200 bg-white/80 backdrop-blur-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3 max-w-6xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-[#FF6800] rounded-lg">
                <Bot className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-800">Tanya LPDP</h1>
                <p className="text-slate-600 text-sm flex items-center gap-2">
                  <Brain className="w-3 h-3" />
                  AI Search dengan sumber terkurasi
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {user ? (
                <>
                  <Badge variant="outline" className="hidden sm:flex items-center gap-1 text-xs">
                    <Shield className="w-3 h-3" />
                    Authenticated
                  </Badge>
                  <Link href="/admin">
                    <Button variant="outline" size="sm">
                      <Settings className="w-4 h-4 mr-2" />
                      Admin Panel
                    </Button>
                  </Link>
                  <Button variant="outline" size="sm" onClick={handleSignOut}>
                    <LogOut className="w-4 h-4 mr-2" />
                    Sign Out
                  </Button>
                </>
              ) : null}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Hero Section */}
        <div className="container mx-auto px-4 py-12 max-w-4xl text-center">
          <div className="mb-8">
            <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-4">
              Tanya LPDP
            </h2>
            <p className="text-lg md:text-xl text-slate-600 mb-2">
              AI Chatbot dengan sumber terkurasi
            </p>
            <p className="text-sm text-slate-500">
              Cari informasi tentang LPDP dari sumber-sumber terpercaya
            </p>
          </div>

          {/* Search Form */}
          <div className="mb-8">
            <SearchBox
              onSearch={handleSearch}
              placeholder="Masukkan pertanyaanmu..."
              isLoading={isLoading}
              size="lg"
              className="max-w-2xl mx-auto"
            />
          </div>

          {/* Suggested Topics */}
          {knowledgeStats && knowledgeStats.topics.length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-slate-700 mb-4 flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-[#FF6800]" />
                Topik Populer
              </h3>
              <div className="flex flex-wrap gap-2 justify-center max-w-3xl mx-auto">
                {knowledgeStats.topics.slice(0, 8).map((topic, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => handleTopicClick(topic)}
                    className="rounded-full border-slate-300 hover:border-[#FF6800] hover:text-[#FF6800] transition-colors"
                  >
                    {topic}
                    <ArrowRight className="w-3 h-3 ml-1" />
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Knowledge Base Info */}
          {knowledgeStats && (
            <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
              <Card className="border-slate-200 shadow-sm">
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center mb-3">
                    <div className="p-3 bg-blue-100 rounded-full">
                      <BookOpen className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>
                  <h4 className="font-semibold text-slate-800 mb-2">Situs Resmi LPDP</h4>
                  <p className="text-sm text-slate-600 mb-4">
                    Informasi resmi dari website LPDP
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTopicClick('situs resmi LPDP')}
                    className="rounded-full"
                  >
                    Jelajahi
                  </Button>
                </CardContent>
              </Card>

              <Card className="border-slate-200 shadow-sm">
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center mb-3">
                    <div className="p-3 bg-green-100 rounded-full">
                      <Brain className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                  <h4 className="font-semibold text-slate-800 mb-2">Situs Resmi Mata Garuda</h4>
                  <p className="text-sm text-slate-600 mb-4">
                    Portal beasiswa dan informasi pendidikan
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTopicClick('mata garuda')}
                    className="rounded-full"
                  >
                    Jelajahi
                  </Button>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Search Suggestions */}
          <div className="mt-12">
            <SearchSuggestions
              onSuggestionClick={handleTopicClick}
              knowledgeStats={knowledgeStats}
            />
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t border-slate-200 bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-6 max-w-6xl">
          <div className="text-center text-sm text-slate-500">
            <p className="mb-2">
              Dibangun oleh <strong>Guntur & Hatta</strong>
            </p>
            <p>
              Cek ulang informasi, AI bisa salah • Data dari sumber terkurasi
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
